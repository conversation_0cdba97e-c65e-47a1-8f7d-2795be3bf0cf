# Docker Image Maintenance and Security Automation

This project provides a robust SBT-based automation tool for maintaining a base Docker image for the Biomed Graph Projection services. It is designed to periodically check for new security advisories,
update an advisory manifest, and create pull requests with the changes on GitHub. It also includes a workflow for building and pushing multi-platform Docker images.

## Core Workflows

The automation is split into three primary workflows, triggered by the SBT commands `releaseIO`, `buildIO`, and `updateIO`, corresponding to the three main tasks.

### 1. Docker Image Build and Release (Command `releaseIO`)

This workflow handles the building and releasing of the Docker image.

1.  **Build**: Uses `docker buildx` to build multi-platform Docker images. It tags the image with both the current project version and `latest`
    and pushes it to a Docker registry upon completion.
2.  **Manifest Truncation**: Before building, it checks if the `Dockerfile` itself has changed since the last release commit. If it has, the `advisoryManifest` is truncated. 
    This forces a fresh, complete re-evaluation of all security advisories the next time the `update` workflow runs.
3.  **Version Bump**: Increments the project version and commits the change to the `version.sbt` file.

### 2. Docker Image Build (Command `buildIO`)

This workflow handles the building of the Docker image but does not release it. It is used for testing and debugging purposes.

1.  **Build**: Uses `docker buildx` to build multi-platform Docker images. It tags the image with both the current project version and `latest`.
2.  **Manifest Truncation**: Before building, it checks if the `Dockerfile` itself has changed since the last release commit. If it has, the `advisoryManifest` is truncated.
3.  **Git Reset**: Resets any local changes that may have been created during the build process to ensure the working directory is clean.

### 3. Security Advisory Update (Command `updateIO`)

This is the main workflow for keeping the base image's security advisories up to date. It's a multi-step process that runs sequentially:

1.  **Environment Checks**: Verifies that Git and Docker are properly configured and running.
2.  **Fetch Advisories**:
    *   Parses the `Dockerfile` to identify the current Java base image.
    *   Spins up a temporary container from this base image and runs a script using `dnf updateinfo` to fetch the latest list of security updates
        for configured severity levels (e.g., `HIGH`, `CRITICAL`).
    *   Compares this new list against the list stored in the `advisoryManifest` file.
3.  **Check for Changes**: If the new advisory list is identical to the current one, the process stops. Otherwise, it proceeds.
4.  **PR Management**:
    *   **Existing PR Check**: Searches GitHub for any existing open PRs with the same title to prevent duplicates.
    *   **Cleanup**: Closes any older, open pull requests that were created by this tool (identified by a specific label).
        This ensures that only the latest set of updates is proposed. It also deletes the old remote git branch.
5.  **Create Changes**:
    *   Creates a new git branch.
    *   Writes the new list of security advisories to the `advisoryManifest` file.
    *   Commits the updated manifest file with a descriptive message.
    *   Pushes the new branch to the remote repository.
6.  **Create Pull Request**:
    *   Makes a request to the GitHub API to create a new pull request.
    *   The PR title and body are dynamically generated to summarize the changes (e.g., "New security advisory...", "Security advisories changed from X to Y...").
    *   Configured reviewers and labels are automatically added to the PR.


## Technical Implementation

*   **Functional Programming**: The entire logic is built on a functional stack using **Cats Effect** for managing side effects (`IO`) and **fs2** for stream processing.
*   **Process Execution**: External commands (like `docker`, `git`) are executed via the **prox** library,
    which provides a safe, typed interface for running system processes.
*   **HTTP Client**: All interactions with the GitHub API are handled by a robust client built with **http4s**.
    This includes creating pull requests, adding reviewers/labels, and searching for existing PRs.
*   **Configuration**: The tool is configured using **Ciris**, which loads configuration from environment variables. 
    SBT settings (`CustomKeys`) are used to pass configuration to the tasks.
*   **Data Parsing**: **Circe** is used for parsing JSON responses from the GitHub API and for constructing request payloads.

## Configuration

The tool requires several environment variables and SBT settings to be configured.

### Environment Variables

| Variable | Description |
|---|---|
| `GH_TOKEN` or `GITHUB_TOKEN` | A GitHub personal access token with `repo` scope for creating pull requests. |
| `DOCKER_USER` | The username for the Docker registry. |
| `DOCKER_TOKEN` | The password or token for the Docker registry. |

### SBT Settings (`CustomKeys.scala`)

| Setting Key | Description |
|---|---|
| `sinceCommit` | The commit to compare against for detecting `Dockerfile` changes. |
| `updateBranch` | The base name for the branch created for pull requests. |
| `javaBaseImage`| The Java base Docker image to scan (e.g., `my-registry/my-java-image`). |
| `dockerImage` | The target Docker image to build and push. |
| `dockerFile` | The path to the project's `Dockerfile`. |
| `platforms` | A sequence of platforms to build for (e.g., `linux/amd64`, `linux/arm64`). |
| `secSeverityList` | The list of security severities to check for (e.g., `HIGH`, `CRITICAL`). |
| `advisoryManifest` | The file where security advisory data is stored. |
| `reviewers` | A list of GitHub usernames to request reviews from on created pull requests. |
| `pullRequestLabels`| Labels to apply to created pull requests. |

