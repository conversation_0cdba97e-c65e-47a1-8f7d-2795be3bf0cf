pipeline {
    agent { label 'docker && persist-ebs && docker-m5large' }

    // Set log rotation, timeout and timestamps in the console
    options {
        disableConcurrentBuilds()
        buildDiscarder(logRotator(numToKeepStr: '20'))
        timestamps()
        timeout(time: 60, unit: 'MINUTES')
    }

    environment {
        SBT_HOME = "${HOME}/.sbt"
        SBT_CREDENTIALS = "/tmp/credentials"
        SBT_REPOSITORIES = "${SBT_HOME}/repositories"
        SBT_BOOT_DIR = "${SBT_HOME}/boot"
        COURSIER_HOME = "${HOME}/.cache/coursier/v1"
        IVY_HOME = "${HOME}/.ivy2"
        SBT_OPTS = "-Xms8G -Xmx8G -Dsbt.override.build.repos=true " +
                    "-Dsbt.banner=false " +
                    "-Dsbt.supershell=false " +
                    "-Dsbt.color=false " +
                    "-Dsbt.repository.config=${SBT_REPOSITORIES} " +
                    "-Dsbt.boot.directory=${SBT_BOOT_DIR} " +
                    "-Dsbt.coursier.home=${COURSIER_HOME} " +
                    "-Dsbt.ivy.home=${IVY_HOME}"

        DOCKER_HOME = "${HOME}/.docker"
        DOCKER_TOOL_AGENTS_REGISTRY = 'docker-cmdp-tool-agents-local.health.artifactory.tio.systems'

        DOCKER_CREDENTIALS_ID = 'health-artifactory-central'
        GIT_CREDENTIALS_ID = 'svc-account-username-password'
    }

    stages {
        stage('Create Sbt Cache Directories') {
            steps {
                sh '''
                    mkdir -p "${SBT_BOOT_DIR}"
                    mkdir -p "${COURSIER_HOME}"
                    mkdir -p "${IVY_HOME}"
                '''
            }
        }

        stage('Login to the Docker Tool Agents Registry') {
            steps {
                script {
                   withCredentials([
                      usernamePassword(
                          credentialsId: DOCKER_CREDENTIALS_ID,
                          usernameVariable: 'DOCKER_USER',
                          passwordVariable: 'DOCKER_TOKEN'
                      )
                  ]) {
                      sh 'echo "$DOCKER_TOKEN" | docker login "$DOCKER_TOOL_AGENTS_REGISTRY" -u $DOCKER_USER --password-stdin'
                  }
                }
            }
        }


        stage('Obtain the GID of the Agent\'s Docker Group') {
            steps {
                script {
                    env.HOST_DOCKER_GID = sh(returnStdout: true, script: 'getent group docker | cut -d: -f3')
                    echo "The GID of the agent\'s docker group is ${env.HOST_DOCKER_GID}."
                }
            }
        }

        stage ('Check for Security Advisory Updates') {
            agent {
                docker {
                    image "${DOCKER_TOOL_AGENTS_REGISTRY}/cmdp/sbt-build-tools-agent:latest"
                    alwaysPull true
                    args "-v /var/run/docker.sock:/var/run/docker.sock:rw,z " +
                            "-v ${env.SBT_HOME}:${env.SBT_HOME}:rw,z " +
                            "-v ${env.IVY_HOME}:${env.IVY_HOME}:rw,z " +
                            "-v ${env.COURSIER_HOME}:${env.COURSIER_HOME}:rw,z " +
                            "-v ${env.DOCKER_HOME}:${env.DOCKER_HOME}:rw,z " +
                            "-u 1000:${env.HOST_DOCKER_GID} " +
                            "--entrypoint=''"
                    reuseNode true
                }
            }

            stages {
               stage('Create SBT Repository Settings') {
                   steps {
                       configFileProvider([
                               configFile(fileId: 'sbt-credentials', variable: 'CREDENTIALS'),
                               configFile(fileId: 'sbt-repositories', variable: 'REPOSITORIES')
                       ]) {
                           sh '''
                               cat ${CREDENTIALS} > ${SBT_CREDENTIALS}
                               cat ${REPOSITORIES} > ${SBT_REPOSITORIES}
                           '''
                       }
                   }
               }

               stage('Fetch and Process Updates') {
                    steps {
                       script {
                           withCredentials([
                               usernamePassword(
                                   credentialsId: GIT_CREDENTIALS_ID,
                                   usernameVariable: 'GH_USER',
                                   passwordVariable: 'GH_TOKEN'
                               ),
                               usernamePassword(
                                   credentialsId: DOCKER_CREDENTIALS_ID,
                                   usernameVariable: 'DOCKER_USER',
                                   passwordVariable: 'DOCKER_TOKEN'
                              )
                           ]) {
                               sh 'sbt updateIO'
                           }
                       }
                   }
               }
            }
        }
    }
}
