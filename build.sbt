import CustomKeys.*
import DockerConfiguration.*
import GitConfiguration.*
import ReleasePluginIO.autoImport.*
import ReleasePluginIODataTypes.StateAction
import StateActions.*
import cats.effect.unsafe.implicits.global
import sbtrelease.ReleasePlugin.autoImport.ReleaseTransformations.*

ThisBuild / organization := "com.elsevier.cmdp"
ThisBuild / scalaVersion := "3.6.3"

lazy val buildIOAction: StateAction = Seq[StateAction](
  initialGitChecks,
  checkDocker,
  checkDockerBuildx,
  loginToSourceDockerRegistries,
  truncateManifestIfDockerfileChanged,
  buildImageWithoutPush,
  gitReset
).composeAll

lazy val buildCommand = Command.command("buildIO")(buildIOAction(_).unsafeRunSync())

lazy val updateIOAction: StateAction = Seq[StateAction](
  initialGitChecks,
  setGitRemoteUrl,
  checkDocker,
  loginToSourceDockerRegistries,
  fetchSecurityAdvisories,
  existingPullRequestCheck,
  closeAnyOpenPullRequests,
  deleteHeadBranchIfExists,
  createAndPushHeadBranch,
  createPullRequest
).composeAll

lazy val updateCommand = Command.command("updateIO")(updateIOAction(_).unsafeRunSync())

lazy val root = (project in file("."))
    .settings(
      name                     := "jdk-streaming-services-docker",
      commands ++= Seq(buildCommand, updateCommand),
      // remove the release command from the list of commands, as it is unnecessary for this project
      commands ~= (_.filterNot(_.nameOption.contains("release"))),
      configReleaseProcessIO   := Seq[ReleaseStepIO](
        configureGitRemoteUrl,
        configureGitUpstreamBranch,
        checkDocker,
        checkDockerBuildx,
        loginToSourceDockerRegistries,
        loginToTargetDockerRegistry,
        truncateManifestIfDockerfileChanged
      ),
      releaseProcessIO         := Seq[ReleaseStepIO](
        inquireVersions,
        setReleaseVersion,
        commitReleaseVersion,
        tagRelease,
        buildAndPushImage,
        setNextVersion,
        commitNextVersion,
        pushChanges
      ),
      releaseTagComment        := "[skip ci] " + releaseTagComment.value,
      releaseCommitMessage     := "[skip ci] " + releaseCommitMessage.value,
      releaseNextCommitMessage := "[skip ci] " + releaseNextCommitMessage.value,
      updateBranch             := "base-image-security-advisories",
      updateBranchPrefix       := "security",
      sinceCommit              := "HEAD~1",
      dockerFile               := baseDirectory.value / "Dockerfile",
      advisoryManifest         := baseDirectory.value / "advisory.manifest",
      javaBaseImage            := "docker-remote.health.artifactory.tio.systems/amazoncorretto:21-al2023-headless",
      dockerImage              := "docker-cmdp-streaming-local.health.artifactory.tio.systems/cmdp/jdk-streaming-service-base",
      platforms                := Seq("linux/amd64", "linux/arm64"),
      secSeverityList          := Seq("Critical", "Important"),
      reviewers                := Seq("okouyad", "pasquereaub"),
      pullRequestLabels        := Seq("security-advisories")
    )
