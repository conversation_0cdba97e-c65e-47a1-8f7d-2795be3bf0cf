import GitHubClientDataTypes.*
import UrlParser.RepoSlug
import cats.effect.{IO, Resource}
import cats.syntax.all.*
import io.circe.Json
import io.circe.generic.auto.*
import io.circe.syntax.*
import org.http4s.*
import org.http4s.circe.CirceEntityCodec.*
import org.http4s.client.Client
import org.http4s.ember.client.EmberClientBuilder
import org.http4s.headers.{Accept, Authorization}
import org.http4s.syntax.all.*
import org.typelevel.ci.CIString

/**
 * A client for interacting with the GitHub API.
 * Provides methods for creating and managing pull requests.
 */
object GitHubClient {

    /**
     * A shared, reusable HTTP client resource.
     * Using a Resource ensures that the client's underlying resources are properly acquired and released.
     */
    private val clientResource: Resource[IO, Client[IO]] = EmberClientBuilder.default[IO].build

    /**
     * Creates a new pull request on GitHub.
     *
     * @param slug  The repository identifier (owner/repo).
     * @param data  The data for the new pull request.
     * @param token The GitHub API token for authentication.
     * @return An IO effect that results in the created PullRequest.
     */
    def createPullRequest(
        slug: RepoSlug,
        data: CreatePullRequestData,
        token: String
    ): IO[PullRequest] =
        clientResource.use(client => new GithubApiClient(client, slug, token).createPullRequest(data))

    /**
     * Searches for pull requests by their exact title.
     *
     * @param slug  The repository identifier (owner/repo).
     * @param title The exact title of the pull request to find.
     * @param state The state of the pull requests to search for (e.g., Open, Closed).
     * @param token The GitHub API token for authentication.
     * @return An IO effect that results in a list of matching PullRequests.
     */
    def getPullRequestsByTitle(
        slug: RepoSlug,
        title: String,
        state: PullRequestState,
        token: String
    ): IO[List[PullRequest]] =
        clientResource.use(client => new GithubApiClient(client, slug, token).getPullRequestsByTitle(title, state))

    /**
     * Closes a specific pull request.
     *
     * @param slug     The repository identifier (owner/repo).
     * @param prNumber The number of the pull request to close.
     * @param token    The GitHub API token for authentication.
     * @return An IO effect that completes when the pull request is closed.
     */
    def closePullRequest(
        slug: RepoSlug,
        prNumber: Int,
        token: String
    ): IO[Unit] =
        clientResource.use(client => new GithubApiClient(client, slug, token).closePullRequest(prNumber))

    /**
     * Finds all pull requests that have a specific label.
     *
     * @param slug  The repository identifier (owner/repo).
     * @param label The label to search for.
     * @param state The state of the pull requests to search for.
     * @param token The GitHub API token for authentication.
     * @return An IO effect that results in a list of matching PullRequests.
     */
    def getPullRequestsByLabel(
        slug: RepoSlug,
        label: String,
        state: PullRequestState,
        token: String
    ): IO[List[PullRequest]] =
        clientResource.use(client => new GithubApiClient(client, slug, token).getPullRequestsByLabel(label, state))

    /**
     * Constructs the default set of HTTP headers required for all GitHub API requests.
     *
     * @param token The GitHub API token.
     * @return A Headers object containing the necessary headers.
     */
    private def defaultHeaders(token: String): Headers = Headers(
      Authorization(Credentials.Token(AuthScheme.Bearer, token)),
      Accept(MediaType.unsafeParse("application/vnd.github+json")),
      Header.Raw(CIString("X-GitHub-Api-Version"), "2022-11-28")
    )

    /**
     * An internal class that encapsulates the logic for making API calls to a specific GitHub repository.
     * This avoids repeatedly passing the same client, slug, and token to each function.
     */
    private class GithubApiClient(client: Client[IO], slug: RepoSlug, token: String) {

        private val baseApiUri = uri"https://api.github.com"
        private val repoUri    = baseApiUri / "repos" / slug.owner / slug.repo

        /** Creates a pull request and then adds reviewers and labels in parallel. */
        def createPullRequest(data: CreatePullRequestData): IO[PullRequest] = {
            val createPrPayload = Json.obj(
              "title" -> data.title.asJson,
              "head"  -> data.headBranch.asJson,
              "base"  -> data.baseBranch.asJson,
              "body"  -> data.body.asJson
            )
            val pullsUri        = repoUri / "pulls"

            // First, create the pull request.
            client.expect[PullRequest](post(pullsUri, createPrPayload)).flatMap { pr =>
                val prNumberStr    = pr.number.toString
                // Then, kick off requests to add reviewers and labels.
                val addReviewersIO = addMetadata(data.reviewers, "reviewers", pullsUri / prNumberStr / "requested_reviewers")
                val addLabelsIO    = addMetadata(data.labels, "labels", repoUri / "issues" / prNumberStr / "labels")
                // Wait for both to complete, then return the original PR object.
                (addReviewersIO, addLabelsIO).parTupled.as(pr)
            }
        }

        /** Searches for pull requests by title using the GitHub search API. */
        def getPullRequestsByTitle(title: String, state: PullRequestState): IO[List[PullRequest]] = {
            val stateQueryPart = state match {
                case PullRequestState.All => None
                case s                    => Some(s"is:${s.value}")
            }
            // Construct the search query with all necessary parts.
            val queryParts     = List(
              Some("is:pr"),
              stateQueryPart,
              Some(s"repo:${slug.owner}/${slug.repo}"),
              Some(s"""in:title "$title"""") // Double quotes for the exact phrase match.
            ).flatten
            val searchUri      = (baseApiUri / "search" / "issues").withQueryParam("q", queryParts.mkString(" "))

            // The search API can sometimes be fuzzy; an extra client-side filter ensures an exact title match.
            client.expect[SearchResult](get(searchUri)).map(_.items.filter(_.title == title))
        }

        /** Closes a pull request by sending a PATCH request to update its state. */
        def closePullRequest(prNumber: Int): IO[Unit] = {
            val closePrPayload = Json.obj("state" -> "closed".asJson)
            val prUri          = repoUri / "pulls" / prNumber.toString
            client.run(patch(prUri, closePrPayload)).use_
        }

        /** Retrieves pull requests by label by querying the issues endpoint. */
        def getPullRequestsByLabel(label: String, state: PullRequestState): IO[List[PullRequest]] = {
            val issuesUri = (repoUri / "issues")
                .withQueryParam("labels", label)
                .withQueryParam("state", state.value)

            // The GitHub API returns both issues and pull requests from this endpoint.
            // We need to filter the results to only include items that are actual pull requests.
            client.expect[List[Json]](get(issuesUri)).map(
              _.filter(_.hcursor.downField("pull_request").succeeded) // A "pull_request" key indicates a PR.
                  .flatMap(_.as[PullRequest].toOption) // Decode the JSON into a PullRequest object.
            )
        }

        /** A helper to add metadata (like reviewers or labels) to a pull request if the item list is not empty. */
        private def addMetadata(items: Seq[String], key: String, uri: Uri): IO[Unit] =
            if (items.nonEmpty) {
                val payload = Json.obj(key -> items.asJson)
                client.run(post(uri, payload)).use_
            } else {
                IO.unit // Do nothing if there are no items to add.
            }

        // --- HTTP Request Helper Methods ---

        private def baseRequest(uri: Uri): Request[IO] =
            Request[IO](uri = uri).withHeaders(defaultHeaders(token))

        private def get(uri: Uri): Request[IO] =
            baseRequest(uri).withMethod(Method.GET)

        private def post(uri: Uri, payload: Json): Request[IO] =
            baseRequest(uri).withMethod(Method.POST).withEntity(payload)

        private def patch(uri: Uri, payload: Json): Request[IO] =
            baseRequest(uri).withMethod(Method.PATCH).withEntity(payload)
    }
}
