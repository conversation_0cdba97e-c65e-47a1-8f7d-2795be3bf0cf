ThisBuild / scalaVersion := "2.12.20"
ThisBuild / scalacOptions += "-Ypartial-unification"
ThisBuild / transitiveClassifiers ++= Seq("sources")

addSbtPlugin("com.github.sbt" % "sbt-release" % "1.4.0")

val http4sVersion  = "0.23.30"
val proxFs2Version = "0.8.0"

libraryDependencies ++= Seq(
  "io.github.vigoo"   %% "prox-core"           % proxFs2Version,
  "io.github.vigoo"   %% "prox-fs2-3"          % proxFs2Version,
  "is.cir"            %% "ciris"               % "3.9.0",
  "com.indoorvivants" %% "scala-uri"           % "4.2.0",
  "org.http4s"        %% "http4s-core"         % http4sVersion,
  "org.http4s"        %% "http4s-ember-client" % http4sVersion,
  "org.http4s"        %% "http4s-dsl"          % http4sVersion,
  "org.http4s"        %% "http4s-circe"        % http4sVersion,
  "org.http4s"        %% "http4s-circe"        % http4sVersion,
  "io.circe"          %% "circe-core"          % "0.14.14",
  "io.circe"          %% "circe-generic"       % "0.14.14",
  "io.circe"          %% "circe-parser"        % "0.14.14"
)
