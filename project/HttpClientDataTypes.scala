object HttpClientDataTypes {

    /**
     * Groups all the necessary data for creating a new pull request.
     * This avoids having a long parameter list in the createPullRequest method.
     */
    final case class CreatePullRequestData(
        headBranch: String,
        baseBranch: String,
        title: String,
        body: String,
        reviewers: Seq[String],
        labels: Seq[String]
    )

    final case class PullRequest(number: Int, title: String, state: String)

    final case class SearchResult(items: List[PullRequest])

    sealed abstract class PullRequestState(val value: String)
    object PullRequestState {
        case object Open   extends PullRequestState("open")
        case object Closed extends PullRequestState("closed")
        case object All    extends PullRequestState("all")
    }

}
