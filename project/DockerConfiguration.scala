import DockerCommands.*
import ReleasePluginIO.autoImport.*
import ReleasePluginIODataTypes.StateAction
import StateActionsDataTypes.*
import cats.effect.IO
import cats.syntax.all.*
import ciris.env
import sbt.{Project, State}

object DockerConfiguration {

    val checkDocker: StateAction = (st: State) =>
        dockerVersion.as(st).handleErrorWith(_ => IO.raiseError(new RuntimeException("Docker not found")))

    val checkDockerBuildx: StateAction = (st: State) =>
        dockerBuildxVersion.as(st).handleErrorWith(_ => IO.raiseError(new RuntimeException("Docker Buildx not found")))

    val loginToTargetDockerRegistry: StateAction = (st: State) =>
        for {
            user           <- env(dockerUser).load[IO]
            token          <- env(dockerToken).load[IO]
            extracted       = Project.extract(st)
            dockerImage     = extracted.get(CustomKeys.dockerImage)
            targetRegistry <- DockerRefParser.extractRegistry(dockerImage)
            _              <- dockerLogin(targetRegistry, user, token)
        } yield st

    val loginToSourceDockerRegistries: StateAction = (st: State) =>
        for {
            user             <- env(dockerUser).load[IO]
            token            <- env(dockerToken).load[IO]
            extracted         = Project.extract(st)
            dockerFile        = extracted.get(CustomKeys.dockerFile)
            dockerFileData    = DockerFileData(dockerFile)
            baseImages       <- dockerFileData.baseImages
            sourceRegistries <- baseImages.traverse(DockerRefParser.extractRegistry)
            _                <- sourceRegistries.traverse_(dockerLogin(_, user, token))
        } yield st
}
