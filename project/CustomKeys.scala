import StateActionsDataTypes.PullRequestData
import sbt.internal.util.AttributeKey
import sbt.{KeyRanks, settingKey}

import java.io.File

object CustomKeys {

    // Setting keys
    val sinceCommit        = settingKey[String]("The commit to compare since for changes")
        .withRank(KeyRanks.Invisible)
    val updateBranch       = settingKey[String]("The branch for creating pull requests")
        .withRank(KeyRanks.Invisible)
    val updateBranchPrefix = settingKey[String]("The prefix for the update branch")
        .withRank(KeyRanks.Invisible)
    val javaBaseImage      = settingKey[String]("The Java base Docker image without a digest")
        .withRank(KeyRanks.Invisible)
    val dockerImage        = settingKey[String]("The Docker image to build and push")
        .withRank(KeyRanks.Invisible)
    val dockerFile         = settingKey[File]("The path to the Dockerfile")
        .withRank(KeyRanks.Invisible)
    val platforms          = settingKey[Seq[String]]("The platforms to build for")
        .withRank(KeyRanks.Invisible)
    val secSeverityList    = settingKey[Seq[String]]("Security severities to check for")
        .withRank(KeyRanks.Invisible)
    val advisoryManifest   = settingKey[File]("The advisory manifest file")
        .withRank(KeyRanks.Invisible)
    val reviewers          = settingKey[Seq[String]]("The list of PR reviewers")
        .withRank(KeyRanks.Invisible)
    val pullRequestLabels  = settingKey[Seq[String]]("The PR label")
        .withRank(KeyRanks.Invisible)

    // Attribute keys
    val pullRequestData = AttributeKey[PullRequestData]("pullRequestData")

}
