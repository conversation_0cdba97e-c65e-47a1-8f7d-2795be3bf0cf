import cats.effect.IO
import fs2.*

import java.nio.file.Path

object DockerCommands extends ProcessIO {
    import ProxSyntax.*

    val dockerVersion: IO[ProcessOutput] = runProcess(proc"docker version")

    val dockerBuildxVersion: IO[ProcessOutput] = runProcess(proc"docker buildx version")

    def dockerLogin(dockerRegistry: String, user: String, token: String): IO[Unit] = {
        val stdin = Stream.emit(token).through(text.utf8.encode)
        val login = proc"docker login $dockerRegistry -u $user --password-stdin"
        runProcessWithRedirectedInput(login < stdin)
    }

    def dockerBuildx(
        platforms: Seq[String],
        tags: Seq[String],
        workDir: Path,
        pushToRegistry: Boolean
    ): IO[Unit] = for {
        platformArg <- if (platforms.isEmpty)
                           IO.raiseError(new IllegalArgumentException("No platforms specified"))
                       else
                           IO.pure(Seq("--platform", platforms.mkString(",")))
        tagArgs     <- if (tags.isEmpty)
                           IO.raiseError(new IllegalArgumentException("No tags specified"))
                       else
                           IO.pure(tags.flatMap(t => Seq("--tag", t)))
        pushArg      = if (pushToRegistry) Seq("--push") else Seq.empty[String]
        allArgs      = Seq("buildx", "build") ++
                           platformArg ++
                           tagArgs ++
                           Seq("--provenance=false") ++
                           pushArg ++
                           Seq(".")
        process      = Process("docker", allArgs.toList).in(workDir)
        _           <- runProcessWithoutOutput(process)
    } yield ()

    def dockerImageSecurityAdvisories(currentBaseImage: String, secSeverityList: Seq[String]): IO[ProcessOutput] = {

        val script =
            """
            |# The `set -euo pipefail` is required to make sure that the script exits with a non-zero exit code.
            |set -euo pipefail
            |
            |args=()
            |for sev in "$@"; do
            |  args+=(--sec-severity="$sev")
            |done
            |
            |# There is a bug in the command "dnf -q updateinfo list security --sec_severity=...".
            |# It returns all security updates instead of just the ones that match the given severity.
            |# Omitting "security" in the command fixes the issue.
            |# The arg "--releasever=latest" is required to make sure that the command returns the latest updates.
            |dnf -q updateinfo list "${args[@]}" --releasever=latest | awk '{ print $1 }' | sort
            |""".stripMargin

        val processArgs = List(
          "run",
          "--rm",
          currentBaseImage,
          "bash",
          "-c",
          script,
          "_" // placeholder for $0 in bash
        ) ++ secSeverityList

        runProcess(Process("docker", processArgs)).map(_.trim)
    }

}
