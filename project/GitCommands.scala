import UrlParser.*
import cats.effect.IO
import ciris.env

import java.io.File
import java.io.File.separator
import scala.annotation.tailrec

object GitCommands extends ProcessIO {

    import ProxSyntax.*

    val gitCurrentBranch: IO[ProcessOutput] =
        runProcess(proc"git rev-parse --abbrev-ref HEAD")

    val gitRemoteUrl: IO[ProcessOutput] =
        runProcess(proc"git config --get remote.origin.url")

    val gitReset: IO[Unit] =
        runProcessWithoutOutput(proc"git reset --hard")

    val gitSetHttpsRemoteUrl: IO[Unit] = for {
        gitUrl <- gitRemoteUrl
        token  <- env("GH_TOKEN").or(env("GITHUB_TOKEN")).load[IO]
        slug   <- extractRepoSlug(gitUrl)
        _      <- runProcessWithoutOutput(proc"git remote set-url origin ${httpsUrl(slug, token)}")
    } yield ()

    val gitSetUpstreamBranch: IO[Unit] = for {
        branch        <- gitCurrentBranch
        upstreamBranch = s"origin/$branch"
        _             <- runProcessWithoutOutput(proc"git branch --set-upstream-to $upstreamBranch")
    } yield ()

    def gitCreateAndCheckoutBranch(branch: String): IO[Unit] =
        runProcessWithoutOutput(proc"git checkout -b $branch")

    def gitCheckoutBranch(branch: String): IO[Unit] =
        runProcessWithoutOutput(proc"git checkout $branch")

    def gitCommit(msg: String): IO[Unit] =
        runProcessWithoutOutput(proc"git commit -m $msg")

    def gitPush(branch: String): IO[Unit] =
        runProcessWithoutOutput(proc"git push origin $branch")

    def gitDiff(fromCommit: String): IO[ProcessOutput] =
        runProcess(proc"git diff --name-status -z -M $fromCommit HEAD")

    def gitAdd(baseDir: File, file: File): IO[Unit] = {
        val path = baseDir.toPath.relativize(file.toPath)
        runProcessWithoutOutput(proc"git add $path")
    }

    def gitSetHttpsRemoteUrl(gitUrl: String, token: String): IO[Unit] =
        for {
            slug <- extractRepoSlug(gitUrl)
            _    <- runProcessWithoutOutput(proc"git remote set-url origin ${httpsUrl(slug, token)}")
        } yield ()

    def gitSetUpstreamBranch(upstreamBranch: String): IO[Unit] =
        runProcessWithoutOutput(proc"git branch --set-upstream-to $upstreamBranch")

    def gitFreshRemoteBranches: IO[Seq[String]] = gitFetch *> gitRemoteBranches

    def gitFetch: IO[Unit] = runProcessWithoutOutput(proc"git fetch")

    def gitRemoteBranches: IO[Seq[String]] = runProcess(proc"git branch -r")
        .map(
          _.linesIterator
              .map(_.trim)
              .filter(line => line.nonEmpty && !line.contains(" -> "))
              .toSeq
        )

    def changedFilePaths(fromCommit: String): IO[Set[String]] = gitDiff(fromCommit).map { diff =>
        @tailrec
        def loop(remaining: List[String], acc: List[String]): List[String] = remaining match {
            // Statuses with one path (Added, Deleted, Modified, etc.)
            case status :: path :: tail if "ADMTUX".contains(status.head) =>
                loop(tail, path :: acc)

            // Statuses with two paths (Renamed, Copied)
            case status :: oldPath :: newPath :: tail if "RC".contains(status.head) =>
                // For renames and copies, both old and new paths can indicate a change in a project.
                loop(tail, newPath :: oldPath :: acc)

            // Base case: successfully parsed all tokens.
            case Nil => acc

            // Stop parsing if a malformed entry is found and return what has been parsed so far.
            case _ => acc
        }

        val tokens = diff.split('\u0000').filter(_.nonEmpty).toList
        loop(tokens, Nil).map(_.replace(separator, "/")).toSet
    }

    def gitDeleteRemoteBranch(branchName: String): IO[Unit] =
        runProcessWithoutOutput(proc"git push origin --delete $branchName")

    private def httpsUrl(slug: RepoSlug, token: String) =
        s"https://x-token:$<EMAIL>/${slug.owner}/${slug.repo}.git"
}
