import cats.effect.IO

object DockerRefParser {

    private val defaultRegistry = "docker.io"

    def extractRegistry(imageRef: String): IO[String] =
        if (imageRef.trim.isEmpty) {
            IO.raiseError(new IllegalArgumentException("Image reference cannot be empty"))
        } else {
            val parts     = imageRef.split("/", 2)
            val candidate = parts(0)

            if (parts.length == 1)
                // No slash, it's a short name from the default registry (e.g., "ubuntu", "ubuntu:latest").
                // A colon here is for a tag, not a port in a registry name.
                // A dot here is part of the image name, not a domain part of a registry.
                IO.pure(defaultRegistry)
            else if (parts(1).trim.isEmpty)
                IO.raiseError(new IllegalArgumentException(s"Invalid image reference: missing repository path after '$candidate'"))
            else if (candidate.contains(".") || candidate.contains(":") || candidate == "localhost")
                IO.pure(candidate)
            else
                IO.pure(defaultRegistry)
        }
}
