import DockerCommands.*
import FileOperations.*
import GitCommands.*
import HttpClientDataTypes.*
import ReleasePluginIO.autoImport.*
import ReleasePluginIODataTypes.StateAction
import StateActionsDataTypes.*
import cats.effect.IO
import cats.syntax.all.*
import ciris.env
import sbt.*
import sbt.Keys.baseDirectory

/**
 * Defines a sequence of custom sbt actions to be executed as part of the build process.
 * These actions handle tasks like checking for security advisories, managing Git branches,
 * and creating pull requests on GitHub.
 */
object StateActions {

    /**
     * An sbt action that truncates the advisory manifest file if the Dockerfile has changed
     * since the last reference commit. This ensures that advisories are re-evaluated
     * whenever the base image or Docker setup is modified.
     */
    lazy val truncateManifestIfDockerfileChanged: StateAction = (st: State) => {
        val extracted    = Project.extract(st)
        val manifestFile = extracted.get(CustomKeys.advisoryManifest)
        val baseDir      = extracted.get(baseDirectory)
        val dockerFile   = extracted.get(CustomKeys.dockerFile)
        val sinceCommit  = extracted.get(CustomKeys.sinceCommit)

        val shouldTruncateIO: IO[Boolean] = (
          isEmptyFile(manifestFile).map(!_),                          // Check if the manifest file is NOT empty.
          hasFileChangedSinceCommit(baseDir, dockerFile, sinceCommit) // Check if the Dockerfile has changed.
        ).mapN(_ && _)                                                // Truncate only if the manifest is not empty AND the Dockerfile has changed.

        shouldTruncateIO.ifM(
          ifTrue  = truncateFile(manifestFile) *>
              gitAdd(baseDir, manifestFile) *>
              st.logInfo(s"Truncated and staged file ${manifestFile.getName}."),
          ifFalse = IO.unit
        ).as(st)
    }

    /** A convenience action to build a Docker image and push it to the configured registry. */
    lazy val buildAndPushImage: StateAction = buildImage(pushToRegistry = true)

    /** A convenience action to build a Docker image without pushing it to a registry. */
    lazy val buildImageWithoutPush: StateAction = buildImage(pushToRegistry = false)

    /** An sbt action to perform a hard reset of the local Git repository. */
    lazy val gitReset: StateAction = (st: State) => GitCommands.gitReset.as(st)

    /**
     * Fetches the latest security advisories for the project's Java base image.
     * If new advisories are found, it gathers all necessary data for creating a pull request
     * and stores it in the sbt state for later actions.
     */
    lazy val fetchSecurityAdvisories: StateAction = (st: State) => {
        val extracted       = Project.extract(st)
        val dockerFile      = extracted.get(CustomKeys.dockerFile)
        val dockerFileData  = DockerFileData(dockerFile)
        val baseImage       = extracted.get(CustomKeys.javaBaseImage)
        val manifestFile    = extracted.get(CustomKeys.advisoryManifest)
        val secSeverityList = extracted.get(CustomKeys.secSeverityList)

        for {
            // Determine the current Java base image from the Dockerfile.
            currentJavaImage <- dockerFileData.currentJavaBaseImage(baseImage)
            _                <- st.logInfo(s"Current Java base image: $currentJavaImage")

            // Read the currently known advisories from the local manifest file.
            currentManifest    <- readFile(manifestFile)
            currentManifestData = ManifestData(currentManifest)
            currentAdvisories  <- currentManifestData.advisories
            _                  <- st.logInfo(s"Current list of security advisories for the current Java base image: $currentAdvisories")

            // Fetch the latest advisories from the remote source.
            _              <- st.logInfo("Fetching the latest security advisories for the current Java base image...")
            newManifest    <- dockerImageSecurityAdvisories(currentJavaImage, secSeverityList)
            newManifestData = ManifestData(newManifest)
            newAdvisories  <- newManifestData.advisories
            _              <- st.logInfo(s"Latest list of security advisories for the current Java base image: $newAdvisories")

            // Compare the new advisories with the current ones.
            nextState <- if (newAdvisories == currentAdvisories)
                             // If there are no changes, log it and finish.
                             st.logInfo("No new security advisories for the current Java base image, completing job.").as(st)
                         else {
                             // If new advisories are found, prepare the data needed to create a pull request.
                             val updateBranchPrefix = extracted.get(CustomKeys.updateBranchPrefix)
                             val updateBranch       = extracted.get(CustomKeys.updateBranch)
                             val headBranch         = s"$updateBranchPrefix/$updateBranch"
                             val baseDir            = extracted.get(baseDirectory)
                             val reviewers          = extracted.get(CustomKeys.reviewers)
                             val labels             = extracted.get(CustomKeys.pullRequestLabels)
                             for {
                                 imageData      <- dockerFileData.imageData(baseImage)
                                 baseBranch     <- gitCurrentBranch
                                 remoteUrl      <- gitRemoteUrl
                                 repoSlug       <- UrlParser.extractRepoSlug(remoteUrl)
                                 token          <- env(ghToken).or(env(githubToken)).load[IO]
                                 pullRequestData = PullRequestData(
                                                     currentManifestData = currentManifestData,
                                                     newManifestData     = newManifestData,
                                                     imageData           = imageData,
                                                     labels              = labels,
                                                     baseBranch          = baseBranch,
                                                     headBranch          = headBranch,
                                                     baseDir             = baseDir,
                                                     advisoryManifest    = manifestFile,
                                                     reviewers           = reviewers,
                                                     slug                = repoSlug,
                                                     token               = token
                                                   )
                                 // Store the prepared data in the sbt state for the next actions.
                             } yield st.put(CustomKeys.pullRequestData, pullRequestData)
                         }
        } yield nextState
    }

    /**
     * Checks if a pull request with the same title already exists.
     * If it does, the process is stopped to avoid creating duplicate PRs.
     * The `pullRequestData` is removed from the state to halt later actions.
     */
    lazy val existingPullRequestCheck: StateAction = withPullRequestData { (st, pullRequestData) =>
        for {
            title        <- pullRequestData.title
            _            <- st.logInfo(s"Checking if a pull request with title '$title' already exists in the repository.")
            pullRequests <- HttpClient.getPullRequestsByTitle(
                              slug  = pullRequestData.slug,
                              title = title,
                              state = PullRequestState.All,
                              token = pullRequestData.token
                            )
            _            <- st.logInfo(s"Pull requests with title '$title':\n${pullRequests.mkString("\n")}\n")
            _            <- if (pullRequests.size > 1)
                                IO.raiseError(new RuntimeException(s"At most one pull request with title '$title' should exist."))
                            else IO.unit
            nextState    <- if (pullRequests.exists(_.title == title))
                                // If PR exists, log and remove data from the state to stop the workflow.
                                st.logInfo(s"A pull request with title '$title' already exists in the repository, completing job.")
                                    .as(st.remove(CustomKeys.pullRequestData))
                            else
                                // Otherwise, continue with the job.
                                st.logInfo(s"A pull request with title '$title' does not exist yet in the repository, continuing with the job.")
                                    .as(st)
        } yield nextState
    }

    /**
     * Finds any existing open pull requests with the same labels and closes them.
     * This ensures that only one automated advisory update PR is open at a time.
     */
    lazy val closeAnyOpenPullRequests: StateAction = withPullRequestData { (st, pullRequestData) =>
        val label = pullRequestData.labels.mkString(",")

        for {
            _            <- st.logInfo(s"Checking if there exists an open pull request with label '$label'.")
            pullRequests <- HttpClient.getPullRequestsByLabel(
                              slug  = pullRequestData.slug,
                              label = label,
                              state = PullRequestState.Open,
                              token = pullRequestData.token
                            )
            _            <- st.logInfo(s"Pull requests with label '$label':\n${pullRequests.mkString("\n")}\n")
            _            <- if (pullRequests.size > 1)
                                // Fail if there's more than one, as this indicates an unexpected state.
                                IO.raiseError(new RuntimeException(s"At most one open pull request with label '$label' should exist."))
                            else if (pullRequests.size == 1)
                                // If one is found, close it.
                                st.logInfo(s"An open pull request with label '$label' found, closing it.") *>
                                    HttpClient.closePullRequest(
                                      slug     = pullRequestData.slug,
                                      prNumber = pullRequests.head.number,
                                      token    = pullRequestData.token
                                    ) *> st.logInfo(s"Closed pull request ${pullRequests.head.number}.")
                            else
                                // If none are found, do nothing.
                                st.logInfo(s"No open pull requests with label '$label' found.")
        } yield st
    }

    /**
     * Checks for and deletes the remote head branch if it already exists.
     * This is a cleanup step to ensure the process can start from a clean slate,
     * in case a previous run failed after pushing the branch.
     */
    lazy val deleteHeadBranchIfExists: StateAction = withPullRequestData { (st, pullRequestData) =>
        val headBranch = pullRequestData.headBranch

        for {
            _              <- st.logInfo(s"Checking if remote branch $headBranch exists.")
            remoteBranches <- GitCommands.gitFreshRemoteBranches
            _              <- st.logInfo(s"Remote branches:\n${remoteBranches.mkString("\n")}")
            _              <- if (remoteBranches.contains(s"origin/$headBranch"))
                                  // If the remote branch exists, delete it.
                                  st.logInfo(s"Deleting Remote branch $headBranch.") *>
                                      gitDeleteRemoteBranch(headBranch) *>
                                      st.logInfo(s"Deleted remote branch $headBranch.")
                              else
                                  st.logInfo(s"Remote branch $headBranch does not exists") *> IO.unit
        } yield st
    }

    /**
     * Creates a new local branch, writes the updated advisory manifest,
     * commits the change, and pushes the branch to the remote repository.
     */
    lazy val createAndPushHeadBranch: StateAction = withPullRequestData { (st, pullRequestData) =>
        val headBranch       = pullRequestData.headBranch
        val baseDir          = pullRequestData.baseDir
        val manifestFile     = pullRequestData.advisoryManifest
        val manifestFileName = manifestFile.getName
        val newManifest      = pullRequestData.newManifestData.manifest

        for {
            // Create and switch to the new local branch.
            _ <- st.logInfo(s"Creating local head branch $headBranch.")
            _ <- gitCreateAndCheckoutBranch(headBranch)
            _ <- st.logInfo(s"Created local head branch $headBranch and checked it out.")

            // Write the new advisory data to the manifest file.
            _ <- st.logInfo(s"Writing new advisories to file $manifestFileName.")
            _ <- writeFile(newManifest, manifestFile)
            _ <- st.logInfo(s"Wrote new advisories to file $manifestFileName")

            // Stage the manifest file for commit.
            _ <- st.logInfo(s"Staging $manifestFileName for commit.")
            _ <- gitAdd(baseDir, manifestFile)
            _ <- st.logInfo(s"Staged $manifestFileName for commit.")

            // Commit the changes with a generated message.
            _             <- st.logInfo(s"Committing changes to $manifestFileName.")
            commitMessage <- pullRequestData.commitMessage
            _             <- gitCommit(commitMessage)
            _             <- st.logInfo(s"Committed changes to $manifestFileName.")

            // Push the new branch to the remote repository.
            _ <- st.logInfo(s"Pushing local branch $headBranch to remote.")
            _ <- gitPush(headBranch)
            _ <- st.logInfo(s"Pushed local branch $headBranch to remote.")
        } yield st
    }

    /**
     * Creates the final pull request on GitHub using the data prepared in previous steps.
     * After creating the PR, it checks out the original base branch.
     */
    lazy val createPullRequest: StateAction = withPullRequestData { (st, pullRequestData) =>
        for {
            title                <- pullRequestData.title
            body                 <- pullRequestData.body
            _                    <- st.logInfo(s"Creating a new pull request with title '$title'...")
            createPullRequestData = CreatePullRequestData(
                                      headBranch = pullRequestData.headBranch,
                                      baseBranch = pullRequestData.baseBranch,
                                      title      = title,
                                      body       = body,
                                      reviewers  = pullRequestData.reviewers,
                                      labels     = pullRequestData.labels
                                    )
            // Make the API call to create the pull request.
            pullRequest          <- HttpClient.createPullRequest(
                                      slug  = pullRequestData.slug,
                                      data  = createPullRequestData,
                                      token = pullRequestData.token
                                    )
            _                    <- st.logInfo(s"Successfully created pull request #${pullRequest.number}.")
            // Return to the original branch as a cleanup step.
            _                    <- gitCheckoutBranch(pullRequestData.baseBranch)
        } yield st
    }

    /**
     * A private helper action to build a Docker image using `docker buildx`.
     *
     * @param pushToRegistry If true, the image will be pushed to the remote registry after building.
     */
    private def buildImage(pushToRegistry: Boolean): StateAction = (st: State) => {
        val extracted   = Project.extract(st)
        val dockerImage = extracted.get(CustomKeys.dockerImage)
        val platforms   = extracted.get(CustomKeys.platforms)
        val versionTag  = extracted.get(sbt.Keys.version)
        val currentTag  = s"$dockerImage:$versionTag"
        val latestTag   = s"$dockerImage:latest"
        val tags        = Seq(currentTag, latestTag)
        val workdir     = extracted.get(baseDirectory).toPath

        val beforeMessage = if (pushToRegistry) s"Building and publishing images $latestTag and $currentTag."
        else s"Building images $latestTag and $currentTag."

        val afterMessage = if (pushToRegistry) s"Built and published images $latestTag and $currentTag."
        else s"Built images $latestTag and $currentTag."

        for {
            _ <- st.logInfo(beforeMessage)
            _ <- dockerBuildx(
                   platforms      = platforms,
                   tags           = tags,
                   workDir        = workdir,
                   pushToRegistry = pushToRegistry
                 )
            _ <- st.logInfo(afterMessage)
        } yield st
    }

    /**
     * A higher-order function to safely execute an action that requires `PullRequestData`.
     * It extracts the data from the sbt state. If the data is not present, it skips the action
     * and returns the original state, effectively acting as a guard.
     *
     * @param action The function to execute if `PullRequestData` is available.
     * @return A `StateAction` that wraps the conditional logic.
     */
    private def withPullRequestData(action: (State, PullRequestData) => IO[State]): StateAction =
        (st: State) => IO(st.get(CustomKeys.pullRequestData)).foldT(IO.pure(st))(data => action(st, data))
}
