import cats.effect.IO
import fs2.*
import fs2.io.file.*

object FileOperations {

    def readFile(path: Path): IO[String] = Files[IO]
        .readAll(path)
        .through(text.utf8.decode)
        .compile
        .string

    def writeFile(content: String, path: Path): IO[Unit] = {
        val createParentDirs = path.parent
            .map(Files[IO].createDirectories)
            .getOrElse(IO.unit)

        createParentDirs *>
            Stream
                .emit(content)
                .through(text.utf8.encode)
                .through(Files[IO].writeAll(path, Flags.Write))
                .compile
                .drain
    }

    def truncateFile(path: Path): IO[Boolean] = Files[IO]
        .open(path, Flags(Flag.Read, Flag.Write)).use(fileHandle =>
            for {
                size         <- fileHandle.size
                wasTruncated <- if (size > 0L) fileHandle.truncate(0L).as(true)
                                else IO.pure(false)
            } yield wasTruncated
        )

    def hasFileChangedSinceCommit(baseDir: Path, file: Path, sinceCommit: String): IO[Boolean] = {
        val filePath = baseDir.relativize(file).toString.replace(java.io.File.separator, "/")
        GitCommands.changedFilePaths(sinceCommit).map(_.contains(filePath))
    }

    def isEmptyFile(path: Path): IO[Boolean] = Files[IO].exists(path).ifM(
      Files[IO].size(path).map(_ == 0L),
      IO.raiseError(new java.io.FileNotFoundException(s"$path does not exist"))
    )

}
