import GitConfiguration.*
import ReleasePluginNextDataTypes.*
import cats.data.Kleisli
import cats.effect.IO
import cats.effect.unsafe.implicits.global
import cats.syntax.all.*
import sbt.*
import sbt.Keys.commands
import sbtrelease.ReleasePlugin.autoImport.{ReleaseStep, releaseProcess}

import scala.language.implicitConversions

object ReleasePluginNext extends AutoPlugin {

    object autoImport {

        val configReleaseProcessIO = settingKey[Seq[ReleaseStepIO]]("The configuration of the release utilities")
            .withRank(KeyRanks.Invisible)

        val releaseProcessIO = settingKey[Seq[ReleaseStepIO]]("The IO release process")
            .withRank(KeyRanks.Invisible)

        implicit def funcToStateAction(func: State => IO[State]): StateAction =
            Kleisli(func)

        implicit def stateActionToReleaseStepIO(action: StateAction): ReleaseStepIO =
            ReleaseStepIO(action)

        implicit def funcToReleaseStepIO(func: State => IO[State]): ReleaseStepIO =
            ReleaseStepIO(Kleisli(func))

        final case class ReleaseStepIO(action: StateAction, check: StateAction = Kleisli(IO.pure))

        implicit def releaseStepToReleaseStepIO(releaseStep: ReleaseStep): ReleaseStepIO =
            ReleaseStepIO(
              action = (st: State) => IO.blocking(releaseStep.action(st)),
              check  = (st: State) => IO.blocking(releaseStep.check(st))
            )

        implicit def stateTransformationToReleaseStepIO(transformation: State => State): ReleaseStepIO =
            ReleaseStepIO((st: State) => IO.blocking(transformation(st)))

        implicit def commandToStateAction(command: Command): StateAction =
            (st: State) =>
                IO.blocking {
                    complete.Parser.parse("", command.parser(st)) match {
                        case Right(cmd) => cmd()
                        case Left(msg)  => sys.error(s"Invalid programmatic input:\n$msg")
                    }
                }

        implicit class StateActionOps(stateActions: Seq[StateAction]) {
            def composeAll: StateAction = ReleasePluginNextDataTypes.composeAll(stateActions)
        }

        object ReleaseAction {

            def releaseAction(
                configKey: SettingKey[Seq[ReleaseStepIO]],
                releaseKey: SettingKey[Seq[ReleaseStepIO]]
            ): StateAction = composeAll(
              Seq(
                initStateAction,
                processSteps(configKey),
                processSteps(releaseKey),
                removeFailureAction
              )
            )

            val releaseCommand: Command = Command.command("release")(st =>
                releaseAction(configReleaseProcessIO, releaseProcessIO)(st).unsafeRunSync()
            )

            private def processSteps(key: SettingKey[Seq[ReleaseStepIO]]): StateAction =
                checkActionForKey(key).andThen(actionForKey(key))

            def checkActionForKey(key: SettingKey[Seq[ReleaseStepIO]]): StateAction = (st: State) =>
                for {
                    steps <- IO(Project.extract(st).get(key))
                    checks = steps.map(_.check).toList
                    _     <- checks.traverse(_(st))
                } yield st

            def actionForKey(key: SettingKey[Seq[ReleaseStepIO]]): StateAction = (st: State) =>
                for {
                    steps     <- IO(Project.extract(st).get(key))
                    actions    = steps.map(_.action).toList
                    nextState <- composeIsolatedActions(actions)(st)
                } yield nextState

        }
    }

    override lazy val trigger: PluginTrigger = allRequirements

    override lazy val requires: Plugins = plugins.JvmPlugin && sbtrelease.ReleasePlugin

    import autoImport.*

    override lazy val projectSettings = Seq(
      configReleaseProcessIO := Seq(configureGitRemoteUrl, configureGitUpstreamBranch),
      releaseProcessIO       := releaseProcess.value.map(releaseStepToReleaseStepIO),
      // replace the release command with our own
      commands ~= (_.filterNot(_.nameOption.contains("release")).:+(ReleaseAction.releaseCommand))
    )
}
