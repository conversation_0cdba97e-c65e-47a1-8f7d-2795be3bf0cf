import cats.effect.IO
import cats.syntax.all.*
import io.lemonlabs.uri.{AbsoluteUrl, PathParts, ScpLikeUrl}

object UrlParser {

    // ── Type aliases ────────────────────────────────────────────────
    type UrlText = String

    // ── DataType ────────────────────────────────────────────────
    case class RepoSlug(owner: String, repo: String)

    // Parse an HTTPS or SSH Git remote
    val extractRepoSlug: UrlText => IO[RepoSlug] = url =>
        for {
            uri      <- IO.fromTry(AbsoluteUrl.parseTry(url).orElse(ScpLikeUrl.parseTry(url)))
            repoSlug <- uri.path match {
                            case PathParts(owner, rawRepo) => RepoSlug(owner, rawRepo.stripSuffix(".git")).pure[IO]
                            case _                         => IO.raiseError(new Exception(s"Invalid remote URL: $url"))
                        }
        } yield repoSlug

}
