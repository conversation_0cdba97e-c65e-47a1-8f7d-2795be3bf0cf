import ReleasePluginIO.autoImport.*
import cats.data.Kleisli
import cats.effect.IO
import sbt.State
import sbtrelease.Compat.FailureCommand
import sbtrelease.ReleasePlugin.autoImport.ReleaseKeys.useDefaults

object ReleasePluginIODataTypes {

    type StateAction = Kleisli[IO, State, State]

    val initStateAction: StateAction = (st: State) =>
        IO(st.copy(onFailure = Some(FailureCommand)).put(useDefaults, true))

    val failureCheckAction: StateAction = (st: State) =>
        filterFailureAction((state: State) => IO(state.copy(onFailure = Some(FailureCommand))))(st)

    val removeFailureAction: StateAction = (st: State) =>
        st.remainingCommands match {
            case FailureCommand :: tail => IO(st.copy(remainingCommands = tail))
            case _                      => IO(st)
        }

    // Apparently, the implementation of `andThen` guarantees stack safety.
    def composeAll(actions: Seq[StateAction]): StateAction =
        actions.foldLeft[StateAction](<PERSON><PERSON><PERSON><PERSON>(IO.pure))((acc, current) => acc.andThen(current))

    def composeIsolatedActions(actions: Seq[StateAction]): StateAction = {
        val process           = actions.map(filterFailureAction)
        val processWithChecks = process.flatMap(Seq(_, failureCheckAction))
        composeAll(processWithChecks)
    }

    def filterFailureAction(action: StateAction): StateAction = (st: State) =>
        st.remainingCommands match {
            case FailureCommand :: _ => IO(st.fail)
            case _                   => action(st).handleError(st.handleError)
        }
}
