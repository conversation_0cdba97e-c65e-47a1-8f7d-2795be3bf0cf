import GitCommands.*
import ReleasePluginIO.autoImport.*
import ReleasePluginIODataTypes.StateAction
import sbt.State
import sbtrelease.ExtraReleaseCommands.initialVcsChecksCommand

object GitConfiguration {

    val initialGitChecks: StateAction = initialVcsChecksCommand

    val setGitRemoteUrl: StateAction = (st: State) => gitSetHttpsRemoteUrl.as(st)

    val configureGitRemoteUrl = ReleaseStepIO(
      action = setGitRemoteUrl,
      check  = initialGitChecks
    )

    val configureGitUpstreamBranch: ReleaseStepIO = ReleaseStepIO(
      action = (st: State) => gitSetUpstreamBranch.as(st),
      check  = initialGitChecks
    )

}
