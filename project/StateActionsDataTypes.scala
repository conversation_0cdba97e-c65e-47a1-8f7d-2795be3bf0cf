import UrlParser.RepoSlug
import cats.effect.IO
import cats.syntax.all.*
import fs2.io.file.{Files, Path}
import fs2.{Stream, text}
import sbt.State

import java.io.File
import scala.language.implicitConversions

object StateActionsDataTypes {

    val ghToken     = "GH_TOKEN"
    val githubToken = "GITHUB_TOKEN"
    val dockerUser  = "DOCKER_USER"
    val dockerToken = "DOCKER_TOKEN"

    final case class ManifestData(manifest: String)

    implicit class ManifestOps(manifestData: ManifestData) {

        def toSet: IO[Set[String]] = Stream
            .emit(manifestData.manifest)
            .covary[IO]
            .through(text.lines)
            .map(_.trim)
            .filter(_.nonEmpty)
            .compile
            .to(Set)

        def advisories: IO[String] = toSet.map(_.toList.sorted.mkString(", "))
    }

    final case class ImageData(image: String, shortDigest: String)

    final case class PullRequestData(
        currentManifestData: ManifestData,
        newManifestData: ManifestData,
        imageData: ImageData,
        labels: Seq[String],
        baseBranch: String,
        headBranch: String,
        baseDir: File,
        advisoryManifest: File,
        reviewers: Seq[String],
        slug: RepoSlug,
        token: String
    )

    implicit class PullRequestDataOps(pullRequestData: PullRequestData) {
        private val snippet =
            s"for Docker image ${pullRequestData.imageData.image}:${pullRequestData.imageData.shortDigest}"

        def title: IO[String] = {
            val currentAdvisoriesIO = pullRequestData.currentManifestData.toSet
            val newAdvisoriesIO     = pullRequestData.newManifestData.toSet

            (currentAdvisoriesIO, newAdvisoriesIO).tupled.flatMap { case (currentSet, newSet) =>
                if (currentSet == newSet) {
                    // This case should ideally not happen if the PR is only triggered on changes.
                    // Raising an error is safer than generating a misleading title.
                    IO.raiseError(new IllegalStateException("Advisory sets are identical. No PR should be generated."))
                } else {
                    val currentAdv = currentSet.toList.sorted.mkString(", ")
                    val newAdv     = newSet.toList.sorted.mkString(", ")

                    val titleMessage =
                        if (currentSet.nonEmpty && newSet.nonEmpty) {
                            s"Security advisories changed from $currentAdv to $newAdv $snippet"
                        } else if (newSet.nonEmpty) {
                            val plural = if (newSet.size > 1) "ies" else "y"
                            s"New security advisor$plural $newAdv $snippet"
                        } else { // newSet is empty and currentSet is not empty
                            val plural = if (currentSet.size > 1) "ies" else "y"
                            s"Remove security advisor$plural $currentAdv $snippet"
                        }
                    IO.pure(titleMessage)
                }
            }
        }

        def body: IO[String] = title

        def commitMessage: IO[String] = title.map(b => s"Update advisory manifest.\n\n$b")
    }

    final case class DockerFileData(file: File)

    implicit class DockerFileOps(dockerFile: DockerFileData) {

        def baseImages: IO[List[String]] =
            Files[IO].readAll(dockerFile.file)
                .through(text.utf8.decode)
                .through(text.lines)
                // Remove comments and trim whitespace from each line
                .map(_.split("#", 2).head.trim)
                .filter(_.toUpperCase.startsWith("FROM "))
                .map { fromLine =>
                    // The line is expected to be "FROM <image>" or "FROM <image> AS <alias>"
                    fromLine.split("""\s+""")(1)
                }
                .compile
                .toList

        def currentJavaBaseImage(javaBaseImage: String): IO[String] =
            baseImages.map(_.find(_.startsWith(javaBaseImage)))
                .foldT[String] {
                    val errMsg =
                        s"Could not find the current Java base image in the Dockerfile: ${dockerFile.file.getAbsolutePath}"
                    IO.raiseError(new RuntimeException(errMsg))
                }(IO.pure)

        def imageData(javaBaseImage: String): IO[ImageData] = for {
            currentImage <- currentJavaBaseImage(javaBaseImage)
            shortDigest  <- currentJavaBaseImageShortDigest(currentImage)
        } yield ImageData(javaBaseImage, shortDigest)

        private def currentJavaBaseImageShortDigest(currentJavaBaseImage: String): IO[String] =
            currentJavaBaseImage.split("@sha256:").lift(1) match {
                case Some(digest) =>
                    IO.pure(digest.take(12))

                case None =>
                    IO.raiseError(
                      new RuntimeException(s"The current Java base image does not have a sha256 digest: $currentJavaBaseImage")
                    )
            }

    }

    // OptionT is not an option
    implicit class FoldIO[T](optionT: IO[Option[T]]) {
        def foldT[S](ifEmpty: => IO[S])(f: T => IO[S]): IO[S] = optionT.flatMap(_.fold(ifEmpty)(f))
    }

    implicit class StateOps(state: State) {
        def logInfo(msg: String): IO[Unit] = IO(state.log.info(msg))
    }

    implicit def fileToPath(file: File): Path = Path.fromNioPath(file.toPath)
}
