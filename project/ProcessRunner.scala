import cats.effect.IO
import io.github.vigoo.prox.*

trait ProcessRunner {

    type ProcessOutput = String

    protected val ProxSyntax: ProxFS2[IO] = ProxFS2[cats.effect.IO]
    import ProxSyntax.*

    implicit val runner: JVMProcessRunner = new JVMProcessRunner

    protected val runProcessWithRedirectedInput: Process.UnboundOEProcess => IO[Unit] = proc =>
        proc.run().flatMap { r =>
            if (r.exitCode.code == 0) IO.unit
            else IO.raiseError(new RuntimeException(s"command failed:  ${r.exitCode.code}"))
        }

    protected val runProcess: Process.UnboundProcess => IO[ProcessOutput] = proc =>
        proc.>#(fs2.text.utf8.decode)
            .run()
            .flatMap { r =>
                if (r.exitCode.code == 0) IO.pure(r.output.trim)
                else IO.raiseError(new RuntimeException(s"command failed: ${r.exitCode.code}"))
            }

    protected val runProcessWithoutOutput: Process.UnboundProcess => IO[Unit] = proc =>
        proc.run().flatMap { r =>
            if (r.exitCode.code == 0) IO.unit
            else IO.raiseError(new RuntimeException(s"command failed: ${r.exitCode.code}"))
        }

}
