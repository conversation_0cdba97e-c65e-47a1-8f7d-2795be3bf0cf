FROM docker-entellect-enrichment-local.health.artifactory.tio.systems/new-relic:latest@sha256:3ce1aade500ebf22d9133c391e40a7a33291fb8c5f555f94f82b0df6e54769a8 AS newrelic
FROM docker-remote.health.artifactory.tio.systems/amazoncorretto:21-al2023-headless@sha256:992773e09f9e7535b77126bc70dd7db55cf3e60b68a876e79fff87ab517c5724 AS base

ARG TARGETPLATFORM
ARG APPLICATION_ROOT=/app
ENV APPLICATION_ROOT=${APPLICATION_ROOT}
ARG CEF_ROOT=/cef

# Add certificates to system trust store.
ADD ./files/ZscalerRootCertificate-2048-SHA256.crt /etc/pki/ca-trust/source/anchors/ZscalerRootCertificate-2048-SHA256.crt
ADD ./files/cert_paloalto-tls-interception-root.crt /etc/pki/ca-trust/source/anchors/cert_paloalto-tls-interception-root.crt

# Import certificates to JDK from the system store and update system trust.
RUN \
    keytool -keystore $JAVA_HOME/lib/security/cacerts -storepass changeit -noprompt -trustcacerts -importcert -alias ZscalerRootCertificate -file /etc/pki/ca-trust/source/anchors/ZscalerRootCertificate-2048-SHA256.crt && \
    keytool -keystore $JAVA_HOME/lib/security/cacerts -storepass changeit -noprompt -trustcacerts -importcert -alias PaloAltoRootCertificate -file /etc/pki/ca-trust/source/anchors/cert_paloalto-tls-interception-root.crt && \
    update-ca-trust

# Apply security updates and clean up in the same layer to reduce image size.
COPY advisory.manifest /tmp/
RUN if [ -s /tmp/advisory.manifest ]; then \
      dnf update -y $(sed 's/^/--advisory=/' /tmp/advisory.manifest) --releasever=latest; \
    fi && \
    dnf clean all && \
    rm -rf /var/cache/dnf /var/cache/yum && \
    rm -rf /tmp/* /var/tmp/*

VOLUME /tmp
WORKDIR ${APPLICATION_ROOT}

# Import newrelic to the working directory
COPY --from=newrelic ${CEF_ROOT}/newrelic.jar ./newrelic.jar
COPY --from=newrelic ${CEF_ROOT}/newrelic.yml ./newrelic.yml

# Copy the entry script and ensure it has the correct permissions.
COPY ./files/docker-entrypoint.sh ./docker-entrypoint.sh
RUN chmod +x ./docker-entrypoint.sh