version         = "3.8.3"
runner.dialect  = Scala212Source3
maxColumn       = 150
align.preset    = most
align.multiline = true
indent.main     = 4
align.openParenDefnSite      = true
newlines.avoidInResultType   = true
danglingParentheses.defnSite = false
newlines.selectChains=keep
align.tokens."+" = [
    { code = "=" },
    { code = "<-" },
    { code = ":=" },
    { code = ":", owner = "Defn.Val" }
]
newlines.source = keep
docstrings.style = keep